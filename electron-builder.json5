{"$schema": "https://raw.githubusercontent.com/electron-userland/electron-builder/master/packages/app-builder-lib/scheme.json", "appId": "com.noti.app", "asar": true, "productName": "<PERSON>i", "directories": {"output": "release/${version}"}, "files": ["dist", "dist-electron"], "npmRebuild": false, "buildDependenciesFromSource": false, "extraMetadata": {"main": "dist-electron/main/index.js"}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}], "artifactName": "${productName}-Mac-${version}-${arch}.${ext}", "category": "public.app-category.productivity"}, "win": {"target": [{"target": "nsis", "arch": ["x64"]}, {"target": "portable", "arch": ["x64"]}], "artifactName": "${productName}-Windows-${version}-${arch}.${ext}", "sign": null}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}, {"target": "rpm", "arch": ["x64"]}], "artifactName": "${productName}-Linux-${version}-${arch}.${ext}", "category": "Office"}, "nsis": {"oneClick": false, "perMachine": false, "allowToChangeInstallationDirectory": true, "deleteAppDataOnUninstall": false}, "appx": {"applicationId": "NotiApp", "displayName": "<PERSON>i", "publisherDisplayName": "Noti Team", "identityName": "NotiTeam.Noti", "publisher": "CN=Noti Team", "backgroundColor": "#464646", "showNameOnTiles": true, "languages": ["en-US"]}}